# تطبيق إدارة الديون

تطبيق شامل لإدارة الديون مطور بلغة Flutter مع قاعدة بيانات SQLite

## المميزات

### الواجهة الرئيسية
- **شريط علوي احترافي** يحتوي على:
  - أزرار الإعدادات والمستخدمين والعملاء
  - زر إضافة دين جديد
  - زر تسديد قسط
  - زر التقارير
  - زر الإشعارات
  - معلومات المستخدم مع الصورة الشخصية

- **بطاقات الملخص** تعرض:
  - إجمالي المبلغ المتبقي
  - إجمالي المبلغ المسدد
  - شعار البرنامج

### قاعدة البيانات
- **SQLite** لحفظ البيانات محلياً
- جداول منفصلة للديون والدفعات
- فهارس محسنة للأداء
- علاقات مترابطة بين الجداول

### النماذج
- **نموذج الدين**: يحتوي على معلومات العميل والمبالغ والتواريخ
- **نموذج الدفعة**: يحتوي على تفاصيل كل دفعة مع طريقة الدفع

## التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي
- **SQLite**: قاعدة البيانات المحلية
- **Material Design**: نظام التصميم
- **Dart**: لغة البرمجة

## كيفية التشغيل

```bash
# تحميل التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## هيكل المشروع

```
lib/
├── models/          # نماذج البيانات
├── database/        # إدارة قاعدة البيانات
├── screens/         # شاشات التطبيق
├── widgets/         # المكونات المخصصة
└── main.dart        # نقطة البداية
```

## الحالة الحالية

✅ **مكتمل:**
- الواجهة الرئيسية الاحترافية
- الشريط العلوي مع جميع الأزرار
- بطاقات الملخص
- قاعدة البيانات SQLite
- نماذج البيانات الأساسية

🔄 **قيد التطوير:**
- شاشات إضافة وتعديل الديون
- شاشة تسديد الأقساط
- شاشة التقارير والإحصائيات
- شاشة إدارة العملاء والمستخدمين
- شاشة الإعدادات

## المطور

تم تطوير هذا التطبيق باستخدام أحدث تقنيات Flutter لضمان الأداء والجودة العالية.
