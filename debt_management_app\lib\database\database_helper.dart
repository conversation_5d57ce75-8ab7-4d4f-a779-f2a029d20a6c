import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/debt.dart';
import '../models/payment.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'debt_management.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // إنشاء جدول الديون
    await db.execute('''
      CREATE TABLE debts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientName TEXT NOT NULL,
        clientPhone TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        paidAmount REAL DEFAULT 0.0,
        remainingAmount REAL NOT NULL,
        createdDate INTEGER NOT NULL,
        dueDate INTEGER,
        notes TEXT,
        status TEXT DEFAULT 'active'
      )
    ''');

    // إنشاء جدول الدفعات
    await db.execute('''
      CREATE TABLE payments(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        debtId INTEGER NOT NULL,
        amount REAL NOT NULL,
        paymentDate INTEGER NOT NULL,
        notes TEXT,
        paymentMethod TEXT DEFAULT 'cash',
        FOREIGN KEY (debtId) REFERENCES debts (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('CREATE INDEX idx_debts_status ON debts(status)');
    await db.execute('CREATE INDEX idx_payments_debt_id ON payments(debtId)');
  }

  // عمليات الديون
  Future<int> insertDebt(Debt debt) async {
    final db = await database;
    return await db.insert('debts', debt.toMap());
  }

  Future<List<Debt>> getAllDebts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('debts');
    return List.generate(maps.length, (i) => Debt.fromMap(maps[i]));
  }

  Future<List<Debt>> getDebtsByStatus(String status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      where: 'status = ?',
      whereArgs: [status],
    );
    return List.generate(maps.length, (i) => Debt.fromMap(maps[i]));
  }

  Future<Debt?> getDebtById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'debts',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Debt.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateDebt(Debt debt) async {
    final db = await database;
    return await db.update(
      'debts',
      debt.toMap(),
      where: 'id = ?',
      whereArgs: [debt.id],
    );
  }

  Future<int> deleteDebt(int id) async {
    final db = await database;
    return await db.delete(
      'debts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // عمليات الدفعات
  Future<int> insertPayment(Payment payment) async {
    final db = await database;
    final paymentId = await db.insert('payments', payment.toMap());
    
    // تحديث المبلغ المدفوع في جدول الديون
    await _updateDebtPaidAmount(payment.debtId);
    
    return paymentId;
  }

  Future<List<Payment>> getPaymentsByDebtId(int debtId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'debtId = ?',
      whereArgs: [debtId],
      orderBy: 'paymentDate DESC',
    );
    return List.generate(maps.length, (i) => Payment.fromMap(maps[i]));
  }

  Future<List<Payment>> getAllPayments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      orderBy: 'paymentDate DESC',
    );
    return List.generate(maps.length, (i) => Payment.fromMap(maps[i]));
  }

  Future<int> deletePayment(int id) async {
    final db = await database;
    
    // الحصول على معلومات الدفعة قبل حذفها
    final payment = await getPaymentById(id);
    
    final result = await db.delete(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    // تحديث المبلغ المدفوع في جدول الديون
    if (payment != null) {
      await _updateDebtPaidAmount(payment.debtId);
    }
    
    return result;
  }

  Future<Payment?> getPaymentById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Payment.fromMap(maps.first);
    }
    return null;
  }

  // تحديث المبلغ المدفوع للدين
  Future<void> _updateDebtPaidAmount(int debtId) async {
    final db = await database;
    
    // حساب إجمالي المبلغ المدفوع
    final result = await db.rawQuery(
      'SELECT SUM(amount) as totalPaid FROM payments WHERE debtId = ?',
      [debtId],
    );
    
    final totalPaid = result.first['totalPaid'] as double? ?? 0.0;
    
    // الحصول على إجمالي المبلغ
    final debt = await getDebtById(debtId);
    if (debt != null) {
      final remainingAmount = debt.totalAmount - totalPaid;
      final status = remainingAmount <= 0 ? 'completed' : 'active';
      
      await db.update(
        'debts',
        {
          'paidAmount': totalPaid,
          'remainingAmount': remainingAmount,
          'status': status,
        },
        where: 'id = ?',
        whereArgs: [debtId],
      );
    }
  }

  // إحصائيات
  Future<Map<String, double>> getSummary() async {
    final db = await database;
    
    final result = await db.rawQuery('''
      SELECT 
        SUM(totalAmount) as totalAmount,
        SUM(paidAmount) as totalPaid,
        SUM(remainingAmount) as totalRemaining
      FROM debts
    ''');
    
    final data = result.first;
    return {
      'totalAmount': data['totalAmount'] as double? ?? 0.0,
      'totalPaid': data['totalPaid'] as double? ?? 0.0,
      'totalRemaining': data['totalRemaining'] as double? ?? 0.0,
    };
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
