class Debt {
  final int? id;
  final String clientName;
  final String clientPhone;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final DateTime createdDate;
  final DateTime? dueDate;
  final String? notes;
  final String status; // 'active', 'completed', 'overdue'

  Debt({
    this.id,
    required this.clientName,
    required this.clientPhone,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.createdDate,
    this.dueDate,
    this.notes,
    this.status = 'active',
  }) : remainingAmount = totalAmount - paidAmount;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'remainingAmount': remainingAmount,
      'createdDate': createdDate.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'notes': notes,
      'status': status,
    };
  }

  factory Debt.fromMap(Map<String, dynamic> map) {
    return Debt(
      id: map['id']?.toInt(),
      clientName: map['clientName'] ?? '',
      clientPhone: map['clientPhone'] ?? '',
      totalAmount: map['totalAmount']?.toDouble() ?? 0.0,
      paidAmount: map['paidAmount']?.toDouble() ?? 0.0,
      createdDate: DateTime.fromMillisecondsSinceEpoch(map['createdDate']),
      dueDate: map['dueDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      notes: map['notes'],
      status: map['status'] ?? 'active',
    );
  }

  Debt copyWith({
    int? id,
    String? clientName,
    String? clientPhone,
    double? totalAmount,
    double? paidAmount,
    DateTime? createdDate,
    DateTime? dueDate,
    String? notes,
    String? status,
  }) {
    return Debt(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      createdDate: createdDate ?? this.createdDate,
      dueDate: dueDate ?? this.dueDate,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'Debt(id: $id, clientName: $clientName, totalAmount: $totalAmount, remainingAmount: $remainingAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Debt && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
