class Payment {
  final int? id;
  final int debtId;
  final double amount;
  final DateTime paymentDate;
  final String? notes;
  final String paymentMethod; // 'cash', 'bank_transfer', 'check'

  Payment({
    this.id,
    required this.debtId,
    required this.amount,
    required this.paymentDate,
    this.notes,
    this.paymentMethod = 'cash',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'debtId': debtId,
      'amount': amount,
      'paymentDate': paymentDate.millisecondsSinceEpoch,
      'notes': notes,
      'paymentMethod': paymentMethod,
    };
  }

  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id']?.toInt(),
      debtId: map['debtId']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      paymentDate: DateTime.fromMillisecondsSinceEpoch(map['paymentDate']),
      notes: map['notes'],
      paymentMethod: map['paymentMethod'] ?? 'cash',
    );
  }

  Payment copyWith({
    int? id,
    int? debtId,
    double? amount,
    DateTime? paymentDate,
    String? notes,
    String? paymentMethod,
  }) {
    return Payment(
      id: id ?? this.id,
      debtId: debtId ?? this.debtId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      notes: notes ?? this.notes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
    );
  }

  @override
  String toString() {
    return 'Payment(id: $id, debtId: $debtId, amount: $amount, paymentDate: $paymentDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Payment && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
