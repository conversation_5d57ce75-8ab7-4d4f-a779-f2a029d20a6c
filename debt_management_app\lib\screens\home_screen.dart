import 'package:flutter/material.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/summary_cards.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // بيانات تجريبية
  double totalRemaining = 125000.0;
  double totalPaid = 75000.0;
  String userName = "أحمد محمد";
  String userImage = "assets/images/user_avatar.png";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Column(
        children: [
          // الشريط العلوي المخصص
          CustomAppBar(
            userName: userName,
            userImage: userImage,
            onSettingsPressed: () {
              // TODO: فتح شاشة الإعدادات
            },
            onUsersPressed: () {
              // TODO: فتح شاشة المستخدمين
            },
            onClientsPressed: () {
              // TODO: فتح شاشة العملاء
            },
            onAddDebtPressed: () {
              // TODO: فتح شاشة إضافة دين جديد
            },
            onPaymentPressed: () {
              // TODO: فتح شاشة تسديد قسط
            },
            onReportsPressed: () {
              // TODO: فتح شاشة التقارير
            },
            onNotificationsPressed: () {
              // TODO: فتح شاشة الإشعارات
            },
          ),
          
          // المحتوى الرئيسي
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // بطاقات الملخص
                  SummaryCards(
                    totalRemaining: totalRemaining,
                    totalPaid: totalPaid,
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // قائمة الديون (سيتم إضافتها لاحقاً)
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 1,
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 64,
                              color: Color(0xFF2196F3),
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد ديون مسجلة',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'اضغط على "إضافة دين جديد" لبدء التسجيل',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
