import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget {
  final String userName;
  final String userImage;
  final VoidCallback onSettingsPressed;
  final VoidCallback onUsersPressed;
  final VoidCallback onClientsPressed;
  final VoidCallback onAddDebtPressed;
  final VoidCallback onPaymentPressed;
  final VoidCallback onReportsPressed;
  final VoidCallback onNotificationsPressed;

  const CustomAppBar({
    super.key,
    required this.userName,
    required this.userImage,
    required this.onSettingsPressed,
    required this.onUsersPressed,
    required this.onClientsPressed,
    required this.onAddDebtPressed,
    required this.onPaymentPressed,
    required this.onReportsPressed,
    required this.onNotificationsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF2196F3),
            Color(0xFF1976D2),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              // الأزرار في الجانب الأيمن
              Expanded(
                child: Row(
                  children: [
                    _buildActionButton(
                      icon: Icons.settings,
                      label: 'الإعدادات',
                      onPressed: onSettingsPressed,
                    ),
                    const SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.people,
                      label: 'المستخدمين',
                      onPressed: onUsersPressed,
                    ),
                    const SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.person_outline,
                      label: 'العملاء',
                      onPressed: onClientsPressed,
                    ),
                    const SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.add_circle_outline,
                      label: 'دين جديد',
                      onPressed: onAddDebtPressed,
                      isHighlighted: true,
                    ),
                    const SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.payment,
                      label: 'تسديد',
                      onPressed: onPaymentPressed,
                    ),
                    const SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.assessment,
                      label: 'التقارير',
                      onPressed: onReportsPressed,
                    ),
                    const SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.notifications_outlined,
                      label: 'الإشعارات',
                      onPressed: onNotificationsPressed,
                    ),
                  ],
                ),
              ),
              
              // معلومات المستخدم في الجانب الأيسر
              Row(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        userName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Text(
                        'مرحباً بك',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 45,
                    height: 45,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Container(
                        color: Colors.white,
                        child: const Icon(
                          Icons.person,
                          color: Color(0xFF2196F3),
                          size: 30,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isHighlighted = false,
  }) {
    return Tooltip(
      message: label,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isHighlighted 
                  ? Colors.white.withOpacity(0.2)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: isHighlighted 
                  ? Border.all(color: Colors.white.withOpacity(0.3))
                  : null,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(height: 2),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
