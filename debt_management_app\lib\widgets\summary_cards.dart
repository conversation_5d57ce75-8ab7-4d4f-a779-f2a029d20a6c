import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class SummaryCards extends StatelessWidget {
  final double totalRemaining;
  final double totalPaid;

  const SummaryCards({
    super.key,
    required this.totalRemaining,
    required this.totalPaid,
  });

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat('#,##0', 'ar');

    return Row(
      children: [
        // بطاقة المبلغ المتبقي
        Expanded(
          child: _buildSummaryCard(
            title: 'المبلغ المتبقي',
            amount: totalRemaining,
            icon: Icons.trending_up,
            color: const Color(0xFFE53E3E),
            backgroundColor: const Color(0xFFFED7D7),
            formatter: formatter,
          ),
        ),

        const SizedBox(width: 16),

        // بطاقة المبلغ المسدد
        Expanded(
          child: _buildSummaryCard(
            title: 'المبلغ المسدد',
            amount: totalPaid,
            icon: Icons.trending_down,
            color: const Color(0xFF38A169),
            backgroundColor: const Color(0xFFC6F6D5),
            formatter: formatter,
          ),
        ),

        const SizedBox(width: 16),

        // شعار البرنامج
        Container(
          width: 120,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.account_balance_wallet,
                size: 32,
                color: Color(0xFF2196F3),
              ),
              SizedBox(height: 8),
              Text(
                'إدارة الديون',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2196F3),
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                'نظام متكامل',
                style: TextStyle(fontSize: 10, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
    required Color backgroundColor,
    required NumberFormat formatter,
  }) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: color, size: 16),
                ),
                const Spacer(),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    '${formatter.format(amount)} ر.س',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
